import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import theme from '../../theme/theme';

const CelebrityCard = ({ 
  name, 
  category, 
  image, 
  votes, 
  onPress, 
  style 
}) => {
  return (
    <TouchableOpacity 
      style={[styles.container, style]} 
      onPress={onPress}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={theme.colors.gradients.glass}
        style={styles.card}
      >
        <View style={styles.imageContainer}>
          <Image 
            source={image} 
            style={styles.image}
            resizeMode="cover"
          />
          
        </View>
        
        <View style={styles.content}>
          <Text style={styles.name} numberOfLines={1}>
            {name}
          </Text>
          <Text style={styles.category} numberOfLines={1}>
            {category}
          </Text>
          {/* {votes && (
            <Text style={styles.votes}>
              {votes} votes
            </Text>
          )} */}
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 120,
    marginRight: 15,
  },
  card: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: theme.colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  imageContainer: {
    height: 140,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '50%',
  },
  content: {
    padding: 12,
    alignItems: 'center',
  },
  name: {
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: 4,
  },
  category: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 6,
  },
  votes: {
    fontSize: 11,
    color: theme.colors.accent,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default CelebrityCard;
