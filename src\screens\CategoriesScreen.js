import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  FlatList,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import GradientView from '../components/ui/GradientView';
import Card from '../components/ui/Card';
import SectionHeader from '../components/ui/SectionHeader';
import dataService from '../services/dataService';
import { CATEGORIES_DATA } from '../data/categories';
import theme from '../theme/theme';

const CategoriesScreen = ({ navigation }) => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const formattedCategories = await dataService.getFormattedCategories();
      setCategories(formattedCategories);
    } catch (error) {
      console.error('Erreur lors du chargement des catégories:', error);
      // Use fallback data from categories.js
      const fallbackCategories = CATEGORIES_DATA.map(category => ({
        id: category.id,
        name: category.name,
        icon: category.icon,
        color: category.color,
        candidates: category.candidates || [],
      }));
      setCategories(fallbackCategories);
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryPress = (category) => {
    navigation.navigate('Dashboard');
  };

  const renderCategoryItem = ({ item: category }) => {
    return (
      <TouchableOpacity
        style={styles.categoryCard}
        onPress={() => handleCategoryPress(category)}
        activeOpacity={0.8}
      >
        <Ionicons name={category.icon} size={20} color="#fff" />
        <Text style={styles.categoryName}>{category.name}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <GradientView gradient="primary" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="chevron-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Catégories</Text>
          <View style={styles.headerRight} />
        </View>

        <ScrollView style={styles.scrollView}>
          {/* Section Header */}
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Les catégories</Text>
            <View style={styles.separator} />
          </View>

          {/* Categories Grid */}
          <View style={styles.categoriesContainer}>
            {loading ? (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>Chargement des catégories...</Text>
              </View>
            ) : (
              <FlatList
                data={categories}
                renderItem={renderCategoryItem}
                keyExtractor={(item) => item.id}
                numColumns={3}
                scrollEnabled={false}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.gridContainer}
              />
            )}
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <View style={styles.bottomNavContainer}>
          <BlurView
            tint="dark"
            intensity={60}
            style={styles.bottomNavBlur}
          >
            <TouchableOpacity
              style={styles.navItem}
              onPress={() => navigation.navigate('Home')}
            >
              <Image
                source={require('../../assets/icons/homeOutline.png')}
                style={styles.navIcon}
                resizeMode="contain"
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.navItem}
              onPress={() => navigation.navigate('Dashboard')}
            >
              <Image
                source={require('../../assets/icons/heartOutline.png')}
                style={styles.navIcon}
                resizeMode="contain"
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.navItem}
              onPress={() => navigation.navigate('Profile')}
            >
              <Image
                source={require('../../assets/icons/userOutline.png')}
                style={styles.navIcon}
                resizeMode="contain"
              />
            </TouchableOpacity>
          </BlurView>
        </View>
      </SafeAreaView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#fff',
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  sectionContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 10,
  },
  separator: {
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    width: '100%',
  },
  categoriesContainer: {
    paddingHorizontal: 20,
    marginBottom: 100, // Add space for bottom navigation
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  gridContainer: {
    paddingVertical: 10,
  },
  categoryCard: {
    backgroundColor: 'rgba(139, 115, 85, 0.8)', // Brownish background like in the image
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 12,
    alignItems: 'center',
    justifyContent: 'center',
    margin: 6,
    flex: 1,
    maxWidth: '30%',
    minHeight: 70,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  categoryName: {
    fontSize: 11,
    fontWeight: '500',
    color: '#fff',
    textAlign: 'center',
    marginTop: 4,
    lineHeight: 14,
  },
  bottomNavContainer: {
    position: 'absolute',
    bottom: 34,
    left: 50,
    right: 50,
    height: 80,
    borderRadius: 25,
    overflow: 'hidden',
  },
  bottomNavBlur: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingTop: 20,
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  navIcon: {
    width: 26,
    height: 26,
    tintColor: '#aaa',
  },
});

export default CategoriesScreen;
