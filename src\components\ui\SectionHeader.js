import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import theme from '../../theme/theme';

const SectionHeader = ({ 
  title, 
  showSeparator = true, 
  rightComponent = null,
  rightText = null,
  onRightPress = null,
  titleStyle = {},
  separatorStyle = {},
  containerStyle = {},
  rightTextStyle = {}
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.headerContent}>
        <Text style={[styles.title, titleStyle]}>
          {title}
        </Text>
        
        {rightComponent && rightComponent}
        
        {rightText && (
          <TouchableOpacity onPress={onRightPress}>
            <Text style={[styles.rightText, rightTextStyle]}>
              {rightText}
            </Text>
          </TouchableOpacity>
        )}
      </View>
      
      {showSeparator && (
        <View style={[styles.separator, separatorStyle]} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 15,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
  },
  rightText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontWeight: '400',
  },
  separator: {
    height: 1.5,
    backgroundColor: theme.colors.textPrimary,
    marginVertical: 15,
    opacity: 0.3,
  },
});

export default SectionHeader;
