import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Image } from 'react-native';
import { BlurView } from 'expo-blur';

// Import screens
import HomeScreen from '../../screens/HomeScreen';
import DashboardScreen from '../../screens/DashboardScreen';
import ProfileScreen from '../../screens/ProfileScreen';

const Tab = createBottomTabNavigator();

const BottomTabNavigator = () => {
	return (
		<Tab.Navigator
			screenOptions={({ route }) => ({
				headerShown: false,
				tabBarIcon: ({ focused }) => {
					let iconSource;

					if (route.name === 'Home') {
						iconSource = focused
							? require('../../../assets/icons/home.png')
							: require('../../../assets/icons/homeOutline.png');
					} else if (route.name === 'Dashboard') {
						iconSource = focused
							? require('../../../assets/icons/heart.png')
							: require('../../../assets/icons/heartOutline.png');
					} else if (route.name === 'Profile') {
						iconSource = focused
							? require('../../../assets/icons/user.png')
							: require('../../../assets/icons/userOutline.png');
					}

					return (
						<Image
							source={iconSource}
							style={{
								width: 26,
								height: 26,
								tintColor: focused ? '#fff' : '#aaa'
							}}
							resizeMode="contain"
						/>
					);
				},
				tabBarActiveTintColor: '#fff',
				tabBarInactiveTintColor: '#aaa',
				tabBarShowLabel: false,
				tabBarStyle: {
					position: 'absolute',
					height: 80,
					borderRadius: 25,
					marginHorizontal: 50,
					marginBottom: 34,
					overflow: 'hidden',
					borderTopWidth: 0,
					paddingTop: 20,
				},
				tabBarBackground: () => (
					<BlurView
						tint="dark"
						intensity={60}
						style={{ flex: 1, backgroundColor: 'rgba(255, 255, 255, 0.3)' }}
					/>
				),
			})}
		>
			<Tab.Screen name="Home" component={HomeScreen} />
			<Tab.Screen name="Dashboard" component={DashboardScreen} />
			<Tab.Screen name="Profile" component={ProfileScreen} />
		</Tab.Navigator>
	);
};

export default BottomTabNavigator;
