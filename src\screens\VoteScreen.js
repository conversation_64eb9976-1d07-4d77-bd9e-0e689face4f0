import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import GradientView from '../components/ui/GradientView';
import Button from '../components/ui/Button';
import { theme } from '../theme/theme';
import { CATEGORIES_DATA, CategoriesUtils } from '../data/categories';

const VoteScreen = ({ navigation, route }) => {
  const { categoryIndex = 0 } = route.params || {};

  // Get categories that have candidates
  const categoriesWithCandidates = CATEGORIES_DATA.filter(cat => cat.candidates && cat.candidates.length > 0);
  const currentCategory = categoriesWithCandidates[categoryIndex];

  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const totalCategories = categoriesWithCandidates.length;

  // Get candidates for current category
  const candidates = currentCategory ? currentCategory.candidates.map((candidate, index) => ({
    ...candidate,
    // Add placeholder images since the data doesn't have them
    image: `https://images.unsplash.com/photo-${
      index % 3 === 0 ? '1494790108755-2616b612b786' :
      index % 3 === 1 ? '1507003211169-0a1dd7228f2d' :
      '1517841905240-472988babdf9'
    }?w=150&h=150&fit=crop&crop=face`,
    position: candidate.description || 'Poste'
  })) : [];

  // If no current category, show error
  if (!currentCategory) {
    return (
      <GradientView>
        <SafeAreaView style={styles.container}>
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Aucune catégorie disponible</Text>
            <Button
              title="Retour"
              onPress={() => navigation.goBack()}
              variant="secondary"
            />
          </View>
        </SafeAreaView>
      </GradientView>
    );
  }

  const handleCandidateSelect = (candidate) => {
    setSelectedCandidate(candidate);
  };

  const handleNext = () => {
    if (!selectedCandidate) {
      Alert.alert('Sélection requise', 'Veuillez sélectionner un candidat avant de continuer.');
      return;
    }

    // Here you would save the vote
    console.log(`Vote saved: Category ${currentCategory.name}, Candidate: ${selectedCandidate.name}`);

    // Navigate to next category or results
    const nextCategoryIndex = categoryIndex + 1;
    if (nextCategoryIndex < totalCategories) {
      // Navigate to next category
      navigation.replace('Vote', { categoryIndex: nextCategoryIndex });
    } else {
      // All categories completed - navigate to completion screen or home
      Alert.alert(
        'Vote terminé!',
        'Merci d\'avoir participé au vote. Vos votes ont été enregistrés.',
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('Home')
          }
        ]
      );
    }
  };

  const renderCandidate = ({ item }) => {
    const isSelected = selectedCandidate?.id === item.id;
    
    return (
      <TouchableOpacity
        style={[styles.candidateCard, isSelected && styles.selectedCard]}
        onPress={() => handleCandidateSelect(item)}
        activeOpacity={0.8}
      >
        <Image source={{ uri: item.image }} style={styles.candidateImage} />
        <Text style={styles.candidateName}>{item.name}</Text>
        <Text style={styles.candidatePosition}>{item.position}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <GradientView>
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Vote</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Category Info */}
        <View style={styles.categoryInfo}>
          <Text style={styles.categoryTitle}>
            Catégorie {categoryIndex + 1}: {currentCategory?.name || 'Chargement...'}
          </Text>
          <Text style={styles.progressText}>
            {categoryIndex + 1}/{totalCategories} catégories complétées
          </Text>
          <Text style={styles.instructionText}>
            Voter pour une seule star par catégorie, une fois sélectionné,{'\n'}
            passez à la catégorie suivante.
          </Text>
        </View>

        {/* Candidates Grid */}
        <FlatList
          data={candidates}
          renderItem={renderCandidate}
          keyExtractor={(item) => item.id.toString()}
          numColumns={3}
          contentContainerStyle={styles.candidatesContainer}
          showsVerticalScrollIndicator={false}
        />

        {/* Next Button */}
        <View style={styles.buttonContainer}>
          <Button
            title="Suivant"
            onPress={handleNext}
            variant="secondary"
            style={styles.nextButton}
            textStyle={styles.nextButtonText}
          />
        </View>
      </SafeAreaView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#fff',
  },
  placeholder: {
    width: 40,
  },
  categoryInfo: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 5,
  },
  progressText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 15,
  },
  instructionText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 20,
  },
  candidatesContainer: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  candidateCard: {
    flex: 1,
    margin: 6,
    backgroundColor: 'rgba(139, 115, 85, 0.8)',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    maxWidth: '30%',
  },
  selectedCard: {
    backgroundColor: 'rgba(139, 115, 85, 1)',
    borderWidth: 2,
    borderColor: '#fff',
  },
  candidateImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginBottom: 8,
  },
  candidateName: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 2,
  },
  candidatePosition: {
    fontSize: 10,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 40,
    left: 20,
    right: 20,
  },
  nextButton: {
    backgroundColor: 'rgba(210, 180, 140, 0.9)',
    borderRadius: 25,
    paddingVertical: 15,
  },
  nextButtonText: {
    color: '#8B7355',
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#fff',
    textAlign: 'center',
    marginBottom: 20,
  },
});

export default VoteScreen;
