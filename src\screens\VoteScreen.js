import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import GradientView from '../components/ui/GradientView';
import Button from '../components/ui/Button';
import { theme } from '../theme/theme';
import apiService from '../services/api';
import AsyncStorage from '@react-native-async-storage/async-storage';

const VoteScreen = ({ navigation, route }) => {
  const { categoryIndex = 0 } = route.params || {};

  // State management
  const [categories, setCategories] = useState([]);
  const [stars, setStars] = useState([]);
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userVotes, setUserVotes] = useState([]);
  const [currentUser, setCurrentUser] = useState(null);

  // Get current category and its stars
  const currentCategory = categories[categoryIndex];
  const currentStars = stars.filter(star =>
    star.category && star.category._id === currentCategory?._id && !star.isDeleted
  );
  const totalCategories = categories.length;

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  // Check if user has already voted in current category
  useEffect(() => {
    if (currentCategory && userVotes.length > 0) {
      const hasVotedInCategory = userVotes.some(vote =>
        vote.category._id === currentCategory._id
      );

      if (hasVotedInCategory) {
        // User has already voted in this category, move to next
        const nextCategoryIndex = categoryIndex + 1;
        if (nextCategoryIndex < totalCategories) {
          navigation.replace('Vote', { categoryIndex: nextCategoryIndex });
        } else {
          navigation.replace('VoteCompletion');
        }
      }
    }
  }, [currentCategory, userVotes, categoryIndex, totalCategories]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get current user
      const userData = await AsyncStorage.getItem('userData');
      if (userData) {
        const user = JSON.parse(userData);
        setCurrentUser(user);

        // Get user's existing votes
        try {
          const votes = await apiService.getUserVotes(user.id);
          setUserVotes(votes);
        } catch (voteError) {
          console.log('Could not load user votes:', voteError.message);
        }
      }

      // Load categories and stars
      const [categoriesData, starsData] = await Promise.all([
        apiService.getCategories(),
        apiService.getStars()
      ]);

      // Filter out deleted categories and only keep those with stars
      const activeCategories = categoriesData.filter(cat => !cat.isDeleted);
      const categoriesWithStars = activeCategories.filter(category =>
        starsData.some(star => star.category && star.category._id === category._id && !star.isDeleted)
      );

      setCategories(categoriesWithStars);
      setStars(starsData);

    } catch (err) {
      console.error('Error loading data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCandidateSelect = (candidate) => {
    setSelectedCandidate(candidate);
  };

  const handleNext = async () => {
    if (!selectedCandidate) {
      Alert.alert('Sélection requise', 'Veuillez sélectionner un candidat avant de continuer.');
      return;
    }

    if (!currentUser) {
      Alert.alert('Erreur', 'Utilisateur non connecté');
      return;
    }

    try {
      // Save the vote for current category
      const voteData = {
        userId: currentUser.id,
        votes: [{
          category: currentCategory._id,
          star: selectedCandidate._id
        }]
      };

      await apiService.submitVote(voteData);
      console.log(`Vote saved: Category ${currentCategory.name}, Star: ${selectedCandidate.name}`);

      // Update local userVotes state to reflect the new vote
      const newVote = {
        category: {
          _id: currentCategory._id,
          name: currentCategory.name
        },
        star: {
          _id: selectedCandidate._id,
          name: selectedCandidate.name
        }
      };
      setUserVotes(prev => [...prev, newVote]);

      // Navigate to next category or results
      const nextCategoryIndex = categoryIndex + 1;
      if (nextCategoryIndex < totalCategories) {
        // Navigate to next category
        navigation.replace('Vote', { categoryIndex: nextCategoryIndex });
      } else {
        // All categories completed - navigate to completion screen
        navigation.replace('VoteCompletion');
      }

    } catch (error) {
      console.error('Error submitting vote:', error);

      // Handle specific error cases
      if (error.message.includes('Already voted')) {
        Alert.alert(
          'Vote déjà effectué',
          'Vous avez déjà voté dans cette catégorie.',
          [
            {
              text: 'Continuer',
              onPress: () => {
                const nextCategoryIndex = categoryIndex + 1;
                if (nextCategoryIndex < totalCategories) {
                  navigation.replace('Vote', { categoryIndex: nextCategoryIndex });
                } else {
                  navigation.replace('VoteCompletion');
                }
              }
            }
          ]
        );
      } else {
        Alert.alert('Erreur', error.message || 'Impossible de sauvegarder le vote');
      }
    }
  };

  const renderCandidate = ({ item }) => {
    const isSelected = selectedCandidate?._id === item._id;

    // Use photoUrl from API or fallback to placeholder
    const imageUri = item.photoUrl || `https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face`;

    return (
      <TouchableOpacity
        style={[styles.candidateCard, isSelected && styles.selectedCard]}
        onPress={() => handleCandidateSelect(item)}
        activeOpacity={0.8}
      >
        <Image source={{ uri: imageUri }} style={styles.candidateImage} />
        <Text style={styles.candidateName}>{item.name}</Text>
        <Text style={styles.candidatePosition}>{item.bio || 'Star'}</Text>
        <View style={styles.voteCountContainer}>
          <Text style={styles.voteCount}>{item.voteCount || 0} votes</Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Loading state
  if (loading) {
    return (
      <GradientView>
        <SafeAreaView style={styles.container}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#fff" />
            <Text style={styles.loadingText}>Chargement des catégories...</Text>
          </View>
        </SafeAreaView>
      </GradientView>
    );
  }

  // Error state
  if (error) {
    return (
      <GradientView>
        <SafeAreaView style={styles.container}>
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Erreur: {error}</Text>
            <Button
              title="Réessayer"
              onPress={loadData}
              variant="secondary"
              style={styles.retryButton}
            />
            <Button
              title="Retour"
              onPress={() => navigation.goBack()}
              variant="secondary"
            />
          </View>
        </SafeAreaView>
      </GradientView>
    );
  }

  // No categories available
  if (!currentCategory || currentStars.length === 0) {
    return (
      <GradientView>
        <SafeAreaView style={styles.container}>
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>
              {!currentCategory ? 'Aucune catégorie disponible' : 'Aucune star disponible dans cette catégorie'}
            </Text>
            <Button
              title="Retour"
              onPress={() => navigation.goBack()}
              variant="secondary"
            />
          </View>
        </SafeAreaView>
      </GradientView>
    );
  }

  return (
    <GradientView>
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Vote</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Category Info */}
        <View style={styles.categoryInfo}>
          <Text style={styles.categoryTitle}>
            Catégorie {categoryIndex + 1}: {currentCategory?.name || 'Chargement...'}
          </Text>
          <Text style={styles.progressText}>
            {userVotes.length}/{totalCategories} catégories complétées
          </Text>
          <Text style={styles.instructionText}>
            Voter pour une seule star par catégorie, une fois sélectionné,{'\n'}
            passez à la catégorie suivante.
          </Text>
        </View>

        {/* Candidates Grid */}
        <FlatList
          data={currentStars}
          renderItem={renderCandidate}
          keyExtractor={(item) => item._id}
          numColumns={3}
          contentContainerStyle={styles.candidatesContainer}
          showsVerticalScrollIndicator={false}
        />

        {/* Next Button */}
        <View style={styles.buttonContainer}>
          <Button
            title="Suivant"
            onPress={handleNext}
            variant="secondary"
            style={styles.nextButton}
            textStyle={styles.nextButtonText}
          />
        </View>
      </SafeAreaView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#fff',
  },
  placeholder: {
    width: 40,
  },
  categoryInfo: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 5,
  },
  progressText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 15,
  },
  instructionText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 20,
  },
  candidatesContainer: {
    paddingHorizontal: 20,
    paddingBottom: 100,
  },
  candidateCard: {
    flex: 1,
    margin: 6,
    backgroundColor: 'rgba(139, 115, 85, 0.8)',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    maxWidth: '30%',
  },
  selectedCard: {
    backgroundColor: 'rgba(139, 115, 85, 1)',
    borderWidth: 2,
    borderColor: '#fff',
  },
  candidateImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginBottom: 8,
  },
  candidateName: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 2,
  },
  candidatePosition: {
    fontSize: 10,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 40,
    left: 20,
    right: 20,
  },
  nextButton: {
    backgroundColor: 'rgba(210, 180, 140, 0.9)',
    borderRadius: 25,
    paddingVertical: 15,
  },
  nextButtonText: {
    color: '#8B7355',
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#fff',
    textAlign: 'center',
    marginBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#fff',
    textAlign: 'center',
    marginTop: 15,
  },
  retryButton: {
    marginBottom: 10,
  },
  voteCountContainer: {
    marginTop: 5,
  },
  voteCount: {
    fontSize: 10,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default VoteScreen;
