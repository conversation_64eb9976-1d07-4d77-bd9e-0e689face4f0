import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import GradientView from '../components/ui/GradientView';
import Button from '../components/ui/Button';
import { theme } from '../theme/theme';

const VoteCompletionScreen = ({ navigation }) => {
  // Countdown timer state - set to a future date for the prize draw
  const [timeLeft, setTimeLeft] = useState({
    days: 2,
    hours: 14,
    minutes: 30
  });

  useEffect(() => {
    // Update countdown every minute
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        let { days, hours, minutes } = prev;
        
        if (minutes > 0) {
          minutes--;
        } else if (hours > 0) {
          hours--;
          minutes = 59;
        } else if (days > 0) {
          days--;
          hours = 23;
          minutes = 59;
        }
        
        return { days, hours, minutes };
      });
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  const handleReturnHome = () => {
    // Navigate back to home screen
    navigation.navigate('MainTabs', { screen: 'Home' });
  };

  return (
    <GradientView>
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Vote</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Success Card */}
        <View style={styles.successCard}>
          {/* Thumbs Up Icon */}
          <View style={styles.iconContainer}>
            <Ionicons name="thumbs-up" size={60} color="#fff" />
          </View>

          {/* Success Message */}
          <Text style={styles.successTitle}>
            Votre Vote A Été Soumis Avec Succès !
          </Text>
          <Text style={styles.successSubtitle}>
            Merci de participer aux Leaders Awards. Votre voix compte !
          </Text>

          {/* Countdown Timer */}
          <View style={styles.countdownContainer}>
            <View style={styles.timeBox}>
              <Text style={styles.timeNumber}>{timeLeft.days}</Text>
              <Text style={styles.timeLabel}>Jours</Text>
            </View>
            <View style={styles.timeBox}>
              <Text style={styles.timeNumber}>{timeLeft.hours}</Text>
              <Text style={styles.timeLabel}>Heures</Text>
            </View>
            <View style={styles.timeBox}>
              <Text style={styles.timeNumber}>{timeLeft.minutes}</Text>
              <Text style={styles.timeLabel}>Minutes</Text>
            </View>
          </View>

          <Text style={styles.countdownText}>
            Temps restant avant le tirage au sort du prix.
          </Text>

          {/* Prize Info */}
          <Text style={styles.prizeInfo}>
            N'oubliez pas, vous pourriez gagner des prix incroyables !{'\n'}
            Restez à l'écoute pour les résultats.
          </Text>
        </View>

        {/* Return Button */}
        <View style={styles.buttonContainer}>
          <Button
            title="🏠 Retour à la page d'accueil"
            onPress={handleReturnHome}
            variant="secondary"
            style={styles.returnButton}
            textStyle={styles.returnButtonText}
          />
        </View>
      </SafeAreaView>
    </GradientView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#fff',
  },
  placeholder: {
    width: 40,
  },
  successCard: {
    backgroundColor: 'rgba(139, 115, 85, 0.9)',
    marginHorizontal: 20,
    borderRadius: 20,
    padding: 30,
    alignItems: 'center',
    marginTop: 40,
  },
  iconContainer: {
    width: 100,
    height: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 15,
  },
  successSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 20,
  },
  countdownContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    width: '100%',
  },
  timeBox: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 12,
    padding: 15,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 5,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  timeNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 5,
  },
  timeLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
  },
  countdownText: {
    fontSize: 14,
    color: '#fff',
    textAlign: 'center',
    marginBottom: 25,
    fontWeight: '500',
  },
  prizeInfo: {
    fontSize: 13,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 18,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 40,
    left: 20,
    right: 20,
  },
  returnButton: {
    backgroundColor: 'rgba(210, 180, 140, 0.9)',
    borderRadius: 25,
    paddingVertical: 15,
  },
  returnButtonText: {
    color: '#8B7355',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default VoteCompletionScreen;
